/* 基础样式 */
#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.app {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 100vh;
}

.app h1 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 600;
}

/* 控制按钮区域 */
.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.controls button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.capture-btn {
  background: #007bff;
  color: white;
}

.capture-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.capture-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 信息提示区域 */
.info {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  max-width: 600px;
  margin: 0 auto;
}

.info p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* 截图展示组件样式 */
.screenshot-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  display: flex;
  flex-direction: column;
  z-index: 9999;
}

.screenshot-viewer.loading {
  justify-content: center;
  align-items: center;
  background: #1a1a1a;
}

.loading-spinner {
  color: #fff;
  font-size: 1.5rem;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  backdrop-filter: blur(10px);
}

.screenshot-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.header-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.close-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.screenshot-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  overflow: hidden;
  position: relative;
}

.screenshot-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  /* 不允许选择 */
  user-select: none;
  /* 不触发鼠标事件 */
  pointer-events: none;
}

/* 选择框样式 */
.selection-box {
  position: absolute;
  border: 2px solid #007bff;
  background-color: rgba(0, 123, 255, 0.2);
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

/* 选择提示样式 */
.selection-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  pointer-events: none;
  z-index: 999;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: center;
  }
  
  .controls button {
    width: 100%;
    max-width: 300px;
  }
  
  .screenshot-header {
    padding: 1rem;
  }
  
  .screenshot-header h2 {
    font-size: 1.2rem;
  }
  
  .info {
    margin: 0 1rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info {
  animation: fadeIn 0.5s ease-out;
}

/* 选中图片显示区域 */
.selected-image-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  max-width: 800px;
  margin: 2rem auto;
  animation: fadeIn 0.5s ease-out;
}

.selected-image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.selected-image-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.clear-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.selected-image-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.selected-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
}

.selected-image-info {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

.selected-image-info p {
  margin: 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}
